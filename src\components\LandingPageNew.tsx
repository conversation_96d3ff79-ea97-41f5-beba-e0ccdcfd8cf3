'use client';

import React, { useState, useEffect, useRef, useCallback, memo } from 'react';
import RealTimeThinkingBox from './RealTimeThinkingBox';

interface LandingPageProps {
  onStartChat: () => void;
}

const LandingPage: React.FC<LandingPageProps> = ({ onStartChat }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [showThinking, setShowThinking] = useState(false);
  const [showBacktest, setShowBacktest] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const scrollRef = useRef<HTMLDivElement>(null);

  // 핵심 기능 기반 단계 정의
  const steps = [
    {
      id: 'intro',
      title: 'AI 투자 분석 플랫폼',
      subtitle: '자연어로 질문하면 AI가 맞춤형 투자 전략을 제안하는 혁신적인 플랫폼',
      description: 'GPT-4, LSTM, GARCH 등 다양한 AI/ML 모델을 활용한 전문적인 투자 분석'
    },
    {
      id: 'rag-matching',
      title: '1차 RAG 기반 산업 매칭',
      subtitle: '사용자 전략을 RAG로 분석해 적정 산업 매칭 (1차 응답)',
      features: [
        '⚡ 즉시 1차 답변 제공',
        '🧠 실시간 thinking box로 AI 사고 과정 시각화',
        '🎯 의도 분류 및 페르소나 기반 맞춤 응답',
        '🔍 RAG 기반 정확한 산업/기업 매칭'
      ],
      icon: '🎯'
    },
    {
      id: 'news-strategy',
      title: '2차 뉴스 데이터 전략 도출',
      subtitle: '사용자 전략과 뉴스 데이터를 활용한 전략 도출 (2차 응답)',
      features: [
        '📰 최신 뉴스 데이터 실시간 분석',
        '📊 감성 분석 기반 시장 심리 파악',
        '🔄 1차 응답 대비 정교한 전략 수정',
        '💡 뉴스 기반 투자 인사이트 제공'
      ],
      icon: '📰'
    },
    {
      id: 'portfolio-test',
      title: 'AI 포트폴리오 테스트',
      subtitle: 'AI의 포트폴리오를 테스트할 수 있는 페이지',
      features: [
        '💾 AI 추천 포트폴리오 자동 저장',
        '📈 과거 데이터 기반 백테스팅',
        '⚠️ 포트폴리오 위험도 분석 및 평가',
        '📋 샤프 비율, 최대 낙폭 등 성과 지표'
      ],
      icon: '📈'
    },
    {
      id: 'speedtraffic',
      title: 'SpeedTraffic™ 즉시 분석',
      subtitle: '개별 주식에 대해 즉시 테스트할 수 있는 speedtraffic',
      features: [
        '📊 MFI, 볼린저 밴드, RSI 기술적 분석 3종',
        '📉 VaR (Value at Risk) 리스크 측정',
        '🏭 산업베타 분석',
        '📈 시장베타 (CAPM) 분석',
        '🚦 직관적인 투자 신호등 + AI 해설'
      ],
      icon: '⚡'
    }
  ];

  // 자동 스크롤 및 애니메이션 효과
  useEffect(() => {
    setIsVisible(true);
    
    const interval = setInterval(() => {
      setCurrentStep(prev => (prev + 1) % steps.length);
    }, 8000);

    return () => clearInterval(interval);
  }, [steps.length]);

  // 애니메이션 효과 (각 섹션별 특별 효과)
  useEffect(() => {
    // RAG 기반 산업 매칭 섹션에서 thinking box 표시
    if (currentStep === 1) {
      setShowThinking(true);
      setTimeout(() => setShowThinking(false), 4000);
    } else {
      setShowThinking(false);
    }

    // 포트폴리오 테스트, SpeedTraffic 섹션에서 결과 표시
    if (currentStep >= 3 && currentStep <= 4) {
      setShowBacktest(true);
      setTimeout(() => setShowBacktest(false), 5000);
    } else {
      setShowBacktest(false);
    }
  }, [currentStep]);

  // 키보드 네비게이션
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowDown' || e.key === ' ') {
        e.preventDefault();
        setCurrentStep(prev => Math.min(prev + 1, steps.length - 1));
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        setCurrentStep(prev => Math.max(prev - 1, 0));
      } else if (e.key === 'Enter') {
        onStartChat();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [onStartChat, steps.length]);

  // Apple 스타일 스크롤 시스템
  const [isScrolling, setIsScrolling] = useState(false);
  const [scrollProgress, setScrollProgress] = useState(0);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const scrollAccumulator = useRef(0);

  const SCROLL_THRESHOLD = 100;
  const SCROLL_DEBOUNCE = 150;

  const handleScroll = useCallback((e: React.WheelEvent) => {
    e.preventDefault();

    const delta = e.deltaY;
    const direction = delta > 0 ? 1 : -1;

    scrollAccumulator.current += Math.abs(delta);
    const progress = Math.min(scrollAccumulator.current / SCROLL_THRESHOLD, 1) * 100;
    setScrollProgress(progress);
    setIsScrolling(true);

    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    if (scrollAccumulator.current >= SCROLL_THRESHOLD) {
      if (direction > 0 && currentStep < steps.length - 1) {
        setCurrentStep(prev => prev + 1);
        scrollAccumulator.current = 0;
        setScrollProgress(0);
      } else if (direction < 0 && currentStep > 0) {
        setCurrentStep(prev => prev - 1);
        scrollAccumulator.current = 0;
        setScrollProgress(0);
      }
    }

    scrollTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false);
      scrollAccumulator.current = 0;
      setScrollProgress(0);
    }, SCROLL_DEBOUNCE);
  }, [currentStep, steps.length]);

  return (
    <div
      className="h-screen overflow-hidden bg-gray-50 text-gray-900 relative cursor-default"
      onWheel={handleScroll}
      ref={scrollRef}
    >
      {/* 배경 그라디언트 */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-purple-50 to-gray-100 opacity-90" />

      {/* 메인 콘텐츠 */}
      <div className="relative z-10 h-full flex flex-col">
        
        {/* 스크롤 진행 상황 인디케이터 */}
        <div className="fixed top-4 right-4 z-50 bg-white/90 backdrop-blur-sm rounded-full px-4 py-2 shadow-lg border border-gray-200/50">
          <div className="flex items-center space-x-2">
            <div className="text-xs text-gray-600">스크롤 진행</div>
            <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
              <div
                className="h-full bg-blue-500 transition-all duration-300 ease-out"
                style={{
                  width: `${scrollProgress}%`,
                  backgroundColor: isScrolling ? '#3b82f6' : '#6b7280'
                }}
              />
            </div>
            <div className="text-xs text-gray-500">{currentStep + 1}/{steps.length}</div>
          </div>
        </div>

        {/* 헤더 */}
        <header className="p-6 flex justify-between items-center backdrop-blur-sm bg-white/80 border-b border-gray-200/50">
          <div className="flex items-center space-x-3">
            <img
              src="/hanyang-logo.png"
              alt="한양대학교 로고"
              className="w-8 h-8 rounded-full shadow-lg"
            />
            <span className="text-xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent">
              AI 투자 분석 플랫폼
            </span>
          </div>
          <button
            onClick={onStartChat}
            className="px-8 py-3 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-full hover:from-blue-600 hover:to-purple-600 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl font-semibold"
          >
            시작하기
          </button>
        </header>

        {/* Apple 스타일 섹션 컨테이너 */}
        <main className="flex-1 relative overflow-hidden">
          <div
            className="flex transition-transform duration-700 ease-in-out h-full"
            style={{
              transform: `translateX(-${currentStep * 100}%)`,
              width: `${steps.length * 100}%`
            }}
          >
            {steps.map((step, index) => (
              <section
                key={step.id}
                className="flex items-center justify-center px-8 py-16"
                style={{ width: `${100 / steps.length}%` }}
              >
                <div className="w-full max-w-7xl mx-auto text-center">
                  {index === 0 ? (
                    // 인트로 섹션
                    <>
                      <h1 className="text-4xl sm:text-6xl md:text-8xl font-bold mb-4 sm:mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        {step.title}
                      </h1>
                      <p className="text-lg sm:text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed px-4">
                        {step.subtitle}
                      </p>
                      <div className="mt-8 sm:mt-12 flex justify-center">
                        <div className="animate-bounce">
                          <svg className="w-6 h-6 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                          </svg>
                        </div>
                      </div>
                    </>
                  ) : (
                    // 기능 섹션들
                    <>
                      <div className="text-6xl mb-6">{step.icon}</div>
                      <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                        {step.title}
                      </h2>
                      <p className="text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto mb-8 leading-relaxed px-4">
                        {step.subtitle}
                      </p>

                      {/* 기능 리스트 */}
                      <div className="grid md:grid-cols-2 gap-4 max-w-4xl mx-auto mb-8">
                        {step.features?.map((feature, featureIndex) => (
                          <div
                            key={featureIndex}
                            className="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-lg border border-gray-200/50 text-left"
                          >
                            <span className="text-gray-700">{feature}</span>
                          </div>
                        ))}
                      </div>

                      {/* 특별 효과 */}
                      {index === 1 && showThinking && (
                        <div className="mt-8 flex justify-center">
                          <RealTimeThinkingBox
                            isVisible={true}
                            realTimeMessages={[
                              {
                                id: 'rag1',
                                text: 'RAG 데이터베이스에서 산업 정보 검색 중...',
                                type: 'search',
                                timestamp: Date.now()
                              },
                              {
                                id: 'rag2',
                                text: '사용자 전략과 산업 매칭 분석 중...',
                                type: 'analysis',
                                timestamp: Date.now() + 1000
                              }
                            ]}
                          />
                        </div>
                      )}

                      {index === 3 && showBacktest && (
                        <div className="mt-8 bg-white/90 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-gray-200/50 max-w-2xl mx-auto">
                          <div className="text-2xl font-bold text-green-600 mb-4">📈 포트폴리오 테스트 완료</div>
                          <div className="text-gray-600 mb-4">AI 포트폴리오 백테스팅 결과</div>
                          <div className="grid grid-cols-3 gap-4 text-center">
                            <div>
                              <div className="text-blue-600 font-bold">+18.5%</div>
                              <div className="text-gray-500 text-xs">연간 수익률</div>
                            </div>
                            <div>
                              <div className="text-orange-600 font-bold">12.3%</div>
                              <div className="text-gray-500 text-xs">변동성</div>
                            </div>
                            <div>
                              <div className="text-purple-600 font-bold">1.85</div>
                              <div className="text-gray-500 text-xs">샤프 비율</div>
                            </div>
                          </div>
                        </div>
                      )}

                      {index === 4 && showBacktest && (
                        <div className="mt-8 bg-white/90 backdrop-blur-sm rounded-2xl p-8 shadow-xl border border-gray-200/50 max-w-2xl mx-auto">
                          <div className="text-2xl font-bold text-green-600 mb-4">⚡ SpeedTraffic 분석 완료</div>
                          <div className="text-gray-600 mb-4">개별 주식 즉시 분석 결과</div>
                          <div className="grid grid-cols-2 gap-4">
                            <div className="bg-green-100 rounded-lg p-3">
                              <div className="text-green-700 font-bold">🟢 매수 신호</div>
                              <div className="text-green-600 text-sm">기술적 분석 3종 긍정</div>
                            </div>
                            <div className="bg-blue-100 rounded-lg p-3">
                              <div className="text-blue-700 font-bold">VaR: 2.3%</div>
                              <div className="text-blue-600 text-sm">일일 최대 손실 예상</div>
                            </div>
                          </div>
                        </div>
                      )}
                    </>
                  )}
                </div>
              </section>
            ))}
          </div>
        </main>

        {/* 하단 네비게이션 */}
        <footer className="p-6 flex justify-center items-center space-x-8 backdrop-blur-sm bg-white/80 border-t border-gray-200/50">
          <div className="flex space-x-3">
            {steps.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentStep(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 transform hover:scale-125 ${
                  currentStep === index ? 'bg-blue-500 shadow-lg' : 'bg-gray-300 hover:bg-gray-400'
                }`}
              />
            ))}
          </div>
          <div className="text-gray-500 text-sm flex items-center space-x-2">
            <span>스크롤하여 다음 단계로</span>
            <svg className="w-4 h-4 animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default memo(LandingPage);
